// processOslUpdatedTask.js - Process osl_updated tasks
import { createClient } from '@supabase/supabase-js';

// Function to process an osl_updated task
export default async function processOslUpdatedTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processOslUpdatedTask.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processOslUpdatedTask.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processOslUpdatedTask.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processOslUpdatedTask.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processOslUpdatedTask.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL updated task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processOslUpdatedTask.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL updated task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processOslUpdatedTask.js] Processing OSL updated for id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the OSL was already uploaded to Shopify and needs to be deleted
    console.log(`[processOslUpdatedTask.js] Checking if OSL id=${oslId} was already uploaded to Shopify...`);
    const { data: oslRecord, error: oslFetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at')
      .eq('id', oslId)
      .maybeSingle();

    if (oslFetchError) {
      const errMsg = `[processOslUpdatedTask.js] Error fetching OSL record: ${oslFetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check OSL Shopify upload status.",
        error: oslFetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[processOslUpdatedTask.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    // If OSL was already uploaded to Shopify, delete it and reset the upload status
    if (oslRecord.shopify_uploaded_at) {
      console.log(`[processOslUpdatedTask.js] OSL id=${oslId} was uploaded to Shopify at ${oslRecord.shopify_uploaded_at}. Enqueueing deletion task...`);

      // Generate the SKU for this OSL (OS + id)
      const oslSku = `OS${oslId}`;

      try {
        // Import the enqueue function for Shopify deletion
        const { enqueueDeleteSkuFromShopifyTask } = await import('./processDeleteSkuFromShopifyTask.js');

        // Enqueue immediate deletion task
        await enqueueDeleteSkuFromShopifyTask(
          supabase,
          oslSku,
          'OSL updated - removing previous version from Shopify',
          new Date() // Schedule immediately
        );

        console.log(`[processOslUpdatedTask.js] Successfully enqueued Shopify deletion task for SKU: ${oslSku}`);
        shopifyDeletionEnqueued = true;
      } catch (deleteEnqueueError) {
        const errMsg = `[processOslUpdatedTask.js] Error enqueueing Shopify deletion task: ${deleteEnqueueError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing Shopify deletion for OSL id=${oslId}`);
        // Continue processing even if deletion enqueueing fails
      }

      // Reset the shopify_uploaded_at to null
      try {
        console.log(`[processOslUpdatedTask.js] Setting shopify_uploaded_at to NULL for OSL id=${oslId}`);
        const { error: resetError } = await supabase
          .from('t_order_sheet_lines')
          .update({
            shopify_uploaded_at: null,
            shopify_product_uploaded_notes: 'Reset due to OSL update - previous version deleted from Shopify'
          })
          .eq('id', oslId);

        if (resetError) {
          const errMsg = `[processOslUpdatedTask.js] Error resetting shopify_uploaded_at: ${resetError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Resetting shopify_uploaded_at for OSL id=${oslId}`);
          // Continue processing even if reset fails
        } else {
          console.log(`[processOslUpdatedTask.js] Successfully reset shopify_uploaded_at to NULL for OSL id=${oslId}`);
        }
      } catch (resetException) {
        const errMsg = `[processOslUpdatedTask.js] Exception resetting shopify_uploaded_at: ${resetException.message}`;
        console.error(errMsg);
        await logError(errMsg, `Resetting shopify_uploaded_at for OSL id=${oslId}`);
        // Continue processing even if reset fails
      }
    } else {
      console.log(`[processOslUpdatedTask.js] OSL id=${oslId} was not previously uploaded to Shopify. No deletion needed.`);
    }

    // Create child tasks
    const tasks = [];
    const taskErrors = [];
    let shopifyDeletionEnqueued = false;

    // 1. Create osl_updated_unlink_discs task
    try {
      console.log(`[processOslUpdatedTask.js] Creating osl_updated_unlink_discs task for OSL id=${oslId}`);
      
      const { data: unlinkTask, error: unlinkError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'osl_updated_unlink_discs',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (unlinkError) {
        const errMsg = `[processOslUpdatedTask.js] Error creating osl_updated_unlink_discs task: ${unlinkError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'osl_updated_unlink_discs',
          error: unlinkError.message
        });
      } else {
        tasks.push({
          task_type: 'osl_updated_unlink_discs',
          task_id: unlinkTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Exception creating osl_updated_unlink_discs task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'osl_updated_unlink_discs',
        error: err.message
      });
    }

    // 2. Create set_inv_osl_to_0 task with a 15-second delay
    try {
      console.log(`[processOslUpdatedTask.js] Creating set_inv_osl_to_0 task for OSL id=${oslId}`);
      
      // Calculate scheduled time (15 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 15);

      const { data: setInvTask, error: setInvError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'set_inv_osl_to_0',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (setInvError) {
        const errMsg = `[processOslUpdatedTask.js] Error creating set_inv_osl_to_0 task: ${setInvError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'set_inv_osl_to_0',
          error: setInvError.message
        });
      } else {
        tasks.push({
          task_type: 'set_inv_osl_to_0',
          task_id: setInvTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Exception creating set_inv_osl_to_0 task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'set_inv_osl_to_0',
        error: err.message
      });
    }

    // 3. Create generate_osl_fields task (immediate)
    try {
      console.log(`[processOslUpdatedTask.js] Creating generate_osl_fields task for OSL id=${oslId}`);

      const { data: generateTask, error: generateError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'generate_osl_fields',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (generateError) {
        const errMsg = `[processOslUpdatedTask.js] Error creating generate_osl_fields task: ${generateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'generate_osl_fields',
          error: generateError.message
        });
      } else {
        tasks.push({
          task_type: 'generate_osl_fields',
          task_id: generateTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Exception creating generate_osl_fields task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'generate_osl_fields',
        error: err.message
      });
    }

    // 4. Create match_osl_to_discs task with a 30-second delay
    try {
      console.log(`[processOslUpdatedTask.js] Creating match_osl_to_discs task for OSL id=${oslId}`);

      // Calculate scheduled time (30 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 30);

      const { data: matchTask, error: matchError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'match_osl_to_discs',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (matchError) {
        const errMsg = `[processOslUpdatedTask.js] Error creating match_osl_to_discs task: ${matchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'match_osl_to_discs',
          error: matchError.message
        });
      } else {
        tasks.push({
          task_type: 'match_osl_to_discs',
          task_id: matchTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Exception creating match_osl_to_discs task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'match_osl_to_discs',
        error: err.message
      });
    }

    // 5. Create check_if_osl_is_ready task with a 45-second delay
    try {
      console.log(`[processOslUpdatedTask.js] Creating check_if_osl_is_ready task for OSL id=${oslId}`);

      // Calculate scheduled time (45 seconds from now)
      const scheduledAt = new Date();
      scheduledAt.setSeconds(scheduledAt.getSeconds() + 45);

      const { data: checkReadyTask, error: checkReadyError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'check_if_osl_is_ready',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (checkReadyError) {
        const errMsg = `[processOslUpdatedTask.js] Error creating check_if_osl_is_ready task: ${checkReadyError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${oslId}`);
        taskErrors.push({
          task_type: 'check_if_osl_is_ready',
          error: checkReadyError.message
        });
      } else {
        tasks.push({
          task_type: 'check_if_osl_is_ready',
          task_id: checkReadyTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedTask.js] Exception creating check_if_osl_is_ready task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating task for OSL id=${oslId}`);
      taskErrors.push({
        task_type: 'check_if_osl_is_ready',
        error: err.message
      });
    }

    // Update task status based on results
    const shopifyMessage = shopifyDeletionEnqueued ? ' Shopify deletion enqueued.' : '';

    if (taskErrors.length === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully created ${tasks.length} child tasks for OSL id=${oslId}.${shopifyMessage}`,
        osl_id: oslId,
        tasks: tasks,
        shopify_deletion_enqueued: shopifyDeletionEnqueued
      });
    } else if (tasks.length === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to create any child tasks.${shopifyMessage}`,
        osl_id: oslId,
        errors: taskErrors,
        shopify_deletion_enqueued: shopifyDeletionEnqueued
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Created ${tasks.length} tasks, failed to create ${taskErrors.length} tasks.${shopifyMessage}`,
        osl_id: oslId,
        tasks: tasks,
        errors: taskErrors,
        shopify_deletion_enqueued: shopifyDeletionEnqueued
      });
    }
  } catch (err) {
    const errMsg = `[processOslUpdatedTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process OSL updated task due to an unexpected error.",
      error: err.message
    });
  }
}
