import { runPhase2Matching } from './phase2InnovaMatching.js';

console.log('Testing Phase 2 matching with new parameters...');

// Test: skip=0, batch=50, maxCandidates=1, minConfidence=60%
runPhase2Matching(0, 50, 1, 60).then(result => {
    console.log('Phase 2 result:', result);
    if (result.success) {
        console.log('✅ Phase 2 matching completed successfully!');
        console.log(`Processed: ${result.processed} OSLs`);
        console.log(`Matches found: ${result.matches}`);
        console.log(`Stats:`, result.stats);
    } else {
        console.error('❌ Phase 2 matching failed:', result.error);
    }
}).catch(error => {
    console.error('Error running Phase 2:', error);
});
