# OSL Updated Shopify Deletion Enhancement

This document describes the enhancement to the `osl_updated` task type that automatically handles Shopify product deletion when an Order Sheet Line (OSL) is updated.

## Overview

When an OSL is updated, the system now:

1. **Checks Upload Status**: Determines if the OSL was previously uploaded to Shopify
2. **Enqueues Deletion**: If uploaded, enqueues a `delete_sku_from_shopify` task to remove the old version
3. **Resets Status**: Sets `shopify_uploaded_at` to NULL so the OSL can be re-uploaded with new data
4. **Continues Processing**: Proceeds with normal OSL updated workflow (unlink discs, generate fields, etc.)

## Why This Enhancement?

When an OSL is updated (e.g., weight range, color, or MPS changes), the existing Shopify product becomes outdated and should be removed before a new version is uploaded. This prevents:

- Duplicate or conflicting products in Shopify
- Outdated product information being displayed to customers
- Inventory sync issues between systems

## Technical Implementation

### Files Modified

- `processOslUpdatedTask.js` - Enhanced to check Shopify upload status and enqueue deletion

### New Logic Flow

```
OSL Updated Task Triggered
         ↓
Check if OSL.shopify_uploaded_at IS NOT NULL
         ↓
    [If YES]
         ↓
Generate SKU: "OS" + OSL.id
         ↓
Enqueue delete_sku_from_shopify task (immediate)
         ↓
Set OSL.shopify_uploaded_at = NULL
         ↓
Set OSL.shopify_product_uploaded_notes = "Reset due to OSL update"
         ↓
    [Continue with normal OSL updated processing]
         ↓
Create child tasks: unlink_discs, set_inv_osl_to_0, generate_osl_fields, etc.
```

## SKU Format

OSL products in Shopify use the SKU format: `OS{id}`

Examples:
- OSL ID 16890 → SKU: `OS16890`
- OSL ID 25431 → SKU: `OS25431`

## Database Changes

The OSL record is updated when deletion is triggered:

```sql
UPDATE t_order_sheet_lines 
SET 
  shopify_uploaded_at = NULL,
  shopify_product_uploaded_notes = 'Reset due to OSL update - previous version deleted from Shopify'
WHERE id = {osl_id};
```

## Task Queue Integration

### New Task Created

When an uploaded OSL is updated, the system creates:

```json
{
  "task_type": "delete_sku_from_shopify",
  "payload": {
    "sku": "OS16890",
    "reason": "OSL updated - removing previous version from Shopify"
  },
  "status": "pending",
  "scheduled_at": "2024-01-15T10:30:00.000Z",  // Immediate
  "enqueued_by": "processOslUpdatedTask"
}
```

### Task Result Enhancement

The `osl_updated` task result now includes Shopify deletion information:

```json
{
  "message": "Successfully created 5 child tasks for OSL id=16890. Shopify deletion enqueued.",
  "osl_id": 16890,
  "shopify_deletion_enqueued": true,
  "tasks": [
    {"task_type": "osl_updated_unlink_discs", "task_id": 12345},
    {"task_type": "set_inv_osl_to_0", "task_id": 12346},
    // ... other child tasks
  ]
}
```

## Monitoring and Troubleshooting

### Check if Deletion was Triggered

```sql
-- Check recent osl_updated tasks with Shopify deletion
SELECT 
  id, 
  status, 
  result->>'shopify_deletion_enqueued' as deletion_enqueued,
  result->>'message' as message
FROM t_task_queue 
WHERE task_type = 'osl_updated' 
  AND created_at > NOW() - INTERVAL '1 hour'
  AND result->>'shopify_deletion_enqueued' = 'true';
```

### Find Deletion Tasks for Specific OSL

```sql
-- Find deletion task for OSL 16890
SELECT * 
FROM t_task_queue 
WHERE task_type = 'delete_sku_from_shopify' 
  AND payload->>'sku' = 'OS16890'
ORDER BY created_at DESC;
```

### Check OSL Upload Status

```sql
-- Check current upload status of OSL
SELECT 
  id,
  shopify_uploaded_at,
  shopify_product_uploaded_notes
FROM t_order_sheet_lines 
WHERE id = 16890;
```

## Error Handling

The system is designed to be resilient:

1. **Deletion Enqueueing Fails**: OSL processing continues, but error is logged
2. **Status Reset Fails**: OSL processing continues, but error is logged  
3. **OSL Not Found**: Task fails with appropriate error message

Errors are logged to `t_error_logs` for debugging.

## Testing

Use the test script to verify functionality:

```bash
# Run the test
node testOslUpdatedShopifyDeletion.js

# Check test results
node testOslUpdatedShopifyDeletion.js check <osl_id> <task_id>
```

## Example Scenarios

### Scenario 1: Weight Range Update

1. OSL 16890 exists with weight range 170-175g, uploaded to Shopify
2. User updates weight range to 165-180g
3. System detects OSL was uploaded, enqueues deletion of `OS16890`
4. Sets `shopify_uploaded_at` to NULL
5. Continues with normal OSL processing
6. Later, when OSL is ready, it will be re-uploaded with new weight range

### Scenario 2: Color Change

1. OSL 25431 exists for Blue discs, uploaded to Shopify
2. User changes color to Red
3. System enqueues deletion of `OS25431` from Shopify
4. Resets upload status
5. Processes color change and re-matches discs
6. OSL will be re-uploaded with Red color specification

### Scenario 3: New OSL (Not Previously Uploaded)

1. New OSL 30001 is created
2. User updates some fields
3. System detects `shopify_uploaded_at` is NULL
4. No deletion needed, proceeds with normal processing
5. OSL will be uploaded when ready

## Integration Points

This enhancement integrates with:

- **OSL Update Triggers**: Database triggers that create `osl_updated` tasks
- **Shopify Deletion System**: Uses existing `delete_sku_from_shopify` infrastructure
- **OSL Publishing Workflow**: Ensures clean slate for re-uploading
- **Inventory Management**: Maintains sync between systems

## Benefits

1. **Data Consistency**: Prevents outdated products in Shopify
2. **Automated Cleanup**: No manual intervention required
3. **Seamless Updates**: OSL changes are reflected accurately in Shopify
4. **Audit Trail**: All actions are logged and trackable
5. **Error Resilience**: System continues processing even if deletion fails

## Future Enhancements

Potential improvements:
- Batch deletion for multiple OSL updates
- Configurable deletion delay
- Shopify product archiving instead of deletion
- Integration with other sales channels (Amazon, eBay, etc.)
