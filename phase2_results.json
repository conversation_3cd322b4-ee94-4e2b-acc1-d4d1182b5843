[{"osl_id": 6817, "mps_id": 14798, "candidate_rank": 1, "osl_mold": "Savant", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Savant", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Savant", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Savant", "innova_plastic_normalized": "Star", "innova_internal_id": 17023, "innova_description": "Star Savant Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Savant", "original": "Star Savant Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 42, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 8767, "mps_id": 15796, "candidate_rank": 1, "osl_mold": "Lion", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Lion", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Lion", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Lion", "innova_plastic_normalized": "DX", "innova_internal_id": 16759, "innova_description": "DX Lion Mid-Range", "innova_parsed": {"plastic": "DX", "mold": "Lion", "original": "DX Lion Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 10907, "mps_id": 16541, "candidate_rank": 1, "osl_mold": "Corvette", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Corvette", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Corvette", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Corvette", "innova_plastic_normalized": "Pro", "innova_internal_id": 16607, "innova_description": "Pro Corvette Distance Driver", "innova_parsed": {"plastic": "Pro", "mold": "Corvette", "original": "Pro Corvette Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 43, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 11855, "mps_id": 524, "candidate_rank": 1, "osl_mold": "Teebird", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Teebird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Teebird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Teebird", "innova_plastic_normalized": "Star", "innova_internal_id": 17025, "innova_description": "Star Teebird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Teebird", "original": "Star Teebird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 920, "mps_id": 323, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "DX", "innova_internal_id": 16465, "innova_description": "DX Aviar Putt & Approach", "innova_parsed": {"plastic": "DX", "mold": "Aviar", "original": "DX Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 58, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 17972, "mps_id": 19442, "candidate_rank": 1, "osl_mold": "RocX3", "osl_plastic": "Champion", "osl_stamp": "Circle Fade Stock", "osl_weights": "173-175", "osl_color": "Varies", "innova_parsed_mold": "RocX3", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "RocX3", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "RocX3", "innova_plastic_normalized": "Champion", "innova_internal_id": 6038, "innova_description": "Champion RocX3 Mid-Range", "innova_parsed": {"plastic": "Champion", "mold": "RocX3", "original": "Champion RocX3 Mid-Range"}, "innova_weights": "170-174", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 58, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1106, "mps_id": 473, "candidate_rank": 1, "osl_mold": "Roc", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Roc", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Roc", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Roc", "innova_plastic_normalized": "DX", "innova_internal_id": 16565, "innova_description": "DX Roc Mid-Range", "innova_parsed": {"plastic": "DX", "mold": "Roc", "original": "DX Roc Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 998, "mps_id": 410, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "DX", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "DX", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "DX", "innova_internal_id": 16665, "innova_description": "DX Katana Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "<PERSON><PERSON>", "original": "DX Katana Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 36, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 931, "mps_id": 331, "candidate_rank": 1, "osl_mold": "Beast", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Beast", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Beast", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Beast", "innova_plastic_normalized": "DX", "innova_internal_id": 16467, "innova_description": "DX Beast Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "Beast", "original": "DX Beast Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1269, "mps_id": 415, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Pro", "innova_internal_id": 16683, "innova_description": "Pro Katana Distance Driver", "innova_parsed": {"plastic": "Pro", "mold": "<PERSON><PERSON>", "original": "Pro Katana Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1368, "mps_id": 368, "candidate_rank": 1, "osl_mold": "Dart", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Dart", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Dart", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Dart", "innova_plastic_normalized": "Champion", "innova_internal_id": 16425, "innova_description": "Champion Dart Putt & Approach", "innova_parsed": {"plastic": "Champion", "mold": "Dart", "original": "Champion Dart Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2362, "mps_id": 493, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "Star", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "Star", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "Star", "innova_internal_id": 16963, "innova_description": "Star Shryke Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "<PERSON><PERSON><PERSON>", "original": "Star Shryke Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2389, "mps_id": 1397, "candidate_rank": 1, "osl_mold": "Firebird", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Firebird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Firebird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Firebird", "innova_plastic_normalized": "Star", "innova_internal_id": 16903, "innova_description": "Star Firebird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Firebird", "original": "Star Firebird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 46, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2386, "mps_id": 456, "candidate_rank": 1, "osl_mold": "Orc", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Orc", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Orc", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Orc", "innova_plastic_normalized": "Star", "innova_internal_id": 17117, "innova_description": "Star Orc Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Orc", "original": "Star Orc Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1299, "mps_id": 554, "candidate_rank": 1, "osl_mold": "Valkyrie", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Valkyrie", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Valkyrie", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Valkyrie", "innova_plastic_normalized": "Pro", "innova_internal_id": 16685, "innova_description": "Pro Valkyrie Fairway Driver", "innova_parsed": {"plastic": "Pro", "mold": "Valkyrie", "original": "Pro Valkyrie Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2371, "mps_id": 435, "candidate_rank": 1, "osl_mold": "Leopard3", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Leopard3", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Leopard 3", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Leopard 3", "innova_plastic_normalized": "Star", "innova_internal_id": 16951, "innova_description": "Star Leopard3 Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Leopard3", "original": "Star Leopard3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 46, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1289, "mps_id": 534, "candidate_rank": 1, "osl_mold": "Tern", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Tern", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Tern", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Tern", "innova_plastic_normalized": "Pro", "innova_internal_id": 16611, "innova_description": "Pro Tern Distance Driver", "innova_parsed": {"plastic": "Pro", "mold": "Tern", "original": "Pro Tern Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2393, "mps_id": 416, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Star", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Star", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Star", "innova_internal_id": 16907, "innova_description": "Star Katana Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "<PERSON><PERSON>", "original": "Star Katana Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 3900, "mps_id": 10182, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "Gstar", "osl_stamp": "First Run", "osl_weights": "100-200", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "G-Star", "innova_internal_id": 16831, "innova_description": "GStar Shryke Distance Driver", "innova_parsed": {"plastic": "GStar", "mold": "<PERSON><PERSON><PERSON>", "original": "GStar Shryke Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 43, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 4716, "mps_id": 13387, "candidate_rank": 1, "osl_mold": "TL3", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "TL 3", "innova_parsed_plastic": "Star", "osl_mold_normalized": "TL 3", "osl_plastic_normalized": "Star", "innova_mold_normalized": "TL 3", "innova_plastic_normalized": "Star", "innova_internal_id": 17035, "innova_description": "Star TL 3 Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "TL 3", "original": "Star TL 3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 7274, "mps_id": 14973, "candidate_rank": 1, "osl_mold": "Bullfrog", "osl_plastic": "XT", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Bullfrog", "innova_parsed_plastic": "XT", "osl_mold_normalized": "Bullfrog", "osl_plastic_normalized": "XT", "innova_mold_normalized": "Bullfrog", "innova_plastic_normalized": "XT", "innova_internal_id": 16921, "innova_description": "XT Bullfrog Putt & Approach", "innova_parsed": {"plastic": "XT", "mold": "Bullfrog", "original": "XT Bullfrog Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 7925, "mps_id": 15280, "candidate_rank": 1, "osl_mold": "Roc3", "osl_plastic": "Champion", "osl_stamp": "Blank", "osl_weights": "150-180", "osl_color": "Varies", "innova_parsed_mold": "Roc3", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Roc 3", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Roc 3", "innova_plastic_normalized": "Champion", "innova_internal_id": 14570, "innova_description": "Champion Roc3 Mid-Range", "innova_parsed": {"plastic": "Champion", "mold": "Roc3", "original": "Champion Roc3 Mid-Range"}, "innova_weights": "140-150", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 57, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 7945, "mps_id": 15743, "candidate_rank": 1, "osl_mold": "TL", "osl_plastic": "DX Glow", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "TL", "innova_parsed_plastic": "DX Classic Glow", "osl_mold_normalized": "TL", "osl_plastic_normalized": "DX Classic Glow", "innova_mold_normalized": "TL", "innova_plastic_normalized": "DX Classic Glow", "innova_internal_id": 16479, "innova_description": "DX Classic Glow TL Fairway Driver", "innova_parsed": {"plastic": "DX Classic Glow", "mold": "TL", "original": "DX Classic Glow TL Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 96, "string_similarity": 30, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 15267, "mps_id": 18694, "candidate_rank": 1, "osl_mold": "Boss", "osl_plastic": "Champion Blizzard", "osl_stamp": "1108 Feet - <PERSON> - World Distance Record Holder", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Boss", "innova_parsed_plastic": "Blizzard Champion", "osl_mold_normalized": "Boss", "osl_plastic_normalized": "Blizzard Champion", "innova_mold_normalized": "Boss", "innova_plastic_normalized": "Blizzard Champion", "innova_internal_id": 16516, "innova_description": "Blizzard Champion Boss Distance Driver", "innova_parsed": {"plastic": "Blizzard Champion", "mold": "Boss", "original": "Blizzard Champion Boss Distance Driver"}, "innova_weights": "151-159", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 58, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 15739, "mps_id": 18793, "candidate_rank": 1, "osl_mold": "<PERSON>winder", "osl_plastic": "Champion", "osl_stamp": "Burst Logo Stock", "osl_weights": "130-139", "osl_color": "Varies", "innova_parsed_mold": "<PERSON>winder", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON>winder", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON>winder", "innova_plastic_normalized": "Champion", "innova_internal_id": 15441, "innova_description": "Champion <PERSON><PERSON><PERSON> Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON>winder", "original": "Champion <PERSON><PERSON><PERSON> Fairway Driver"}, "innova_weights": "100-139 (parsed_less_than)", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 56, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 18163, "mps_id": 19501, "candidate_rank": 1, "osl_mold": "Mamba", "osl_plastic": "Gstar", "osl_stamp": "Burst Logo Stock", "osl_weights": "130-139", "osl_color": "Varies", "innova_parsed_mold": "Mamba", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "Mamba", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "Mamba", "innova_plastic_normalized": "G-Star", "innova_internal_id": 14899, "innova_description": "GStar Mamba Distance Driver", "innova_parsed": {"plastic": "GStar", "mold": "Mamba", "original": "GStar Mamba Distance Driver"}, "innova_weights": "100-139 (parsed_less_than)", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 914, "mps_id": 307, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "DX", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "DX", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "DX", "innova_internal_id": 16463, "innova_description": "DX Archangel Fairway Driver", "innova_parsed": {"plastic": "DX", "mold": "<PERSON><PERSON><PERSON>", "original": "DX Archangel Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 10908, "mps_id": 16558, "candidate_rank": 1, "osl_mold": "Mamba", "osl_plastic": "DX", "osl_stamp": "Happy Holidays 2020 Holly Prime Star", "osl_weights": "170-175", "osl_color": "Varies", "innova_parsed_mold": "Mamba", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Mamba", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Mamba", "innova_plastic_normalized": "DX", "innova_internal_id": 13776, "innova_description": "DX Mamba Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "Mamba", "original": "DX Mamba Distance Driver"}, "innova_weights": "170-172", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 11933, "mps_id": 17151, "candidate_rank": 1, "osl_mold": "Destroyer", "osl_plastic": "Star", "osl_stamp": "Stock Stamp (starting 2022)", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Destroyer", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Destroyer", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Destroyer", "innova_plastic_normalized": "Star", "innova_internal_id": 16901, "innova_description": "Star Destroyer Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Destroyer", "original": "Star Destroyer Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 12949, "mps_id": 17697, "candidate_rank": 1, "osl_mold": "Tern", "osl_plastic": "Gstar", "osl_stamp": "Stock Character", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Tern", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "Tern", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "Tern", "innova_plastic_normalized": "G-Star", "innova_internal_id": 16795, "innova_description": "GStar Tern Distance Driver", "innova_parsed": {"plastic": "GStar", "mold": "Tern", "original": "GStar Tern Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 13623, "mps_id": 17987, "candidate_rank": 1, "osl_mold": "Valkyrie", "osl_plastic": "DX Glow", "osl_stamp": "Stock Character", "osl_weights": "130-139", "osl_color": "Varies", "innova_parsed_mold": "Valkyrie", "innova_parsed_plastic": "DX Classic Glow", "osl_mold_normalized": "Valkyrie", "osl_plastic_normalized": "DX Classic Glow", "innova_mold_normalized": "Valkyrie", "innova_plastic_normalized": "DX Classic Glow", "innova_internal_id": 15548, "innova_description": "DX Classic Glow Valkyrie Fairway Driver", "innova_parsed": {"plastic": "DX Classic Glow", "mold": "Valkyrie", "original": "DX Classic Glow Valkyrie Fairway Driver"}, "innova_weights": "100-139 (parsed_less_than)", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 16950, "mps_id": 19123, "candidate_rank": 1, "osl_mold": "Firebird", "osl_plastic": "Star", "osl_stamp": "InnVision Full Color Print", "osl_weights": "165-169", "osl_color": "Varies", "innova_parsed_mold": "Firebird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Firebird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Firebird", "innova_plastic_normalized": "Star", "innova_internal_id": 6269, "innova_description": "Star Firebird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Firebird", "original": "Star Firebird Fairway Driver"}, "innova_weights": "165-169", "mold_match": true, "plastic_match": true, "weight_match": "EXACT_MATCH", "confidence_score": 100, "string_similarity": 46, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 16952, "mps_id": 19123, "candidate_rank": 1, "osl_mold": "Firebird", "osl_plastic": "Star", "osl_stamp": "InnVision Full Color Print", "osl_weights": "173-175", "osl_color": "Varies", "innova_parsed_mold": "Firebird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Firebird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Firebird", "innova_plastic_normalized": "Star", "innova_internal_id": 6271, "innova_description": "Star Firebird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Firebird", "original": "Star Firebird Fairway Driver"}, "innova_weights": "173-175", "mold_match": true, "plastic_match": true, "weight_match": "EXACT_MATCH", "confidence_score": 100, "string_similarity": 46, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1395, "mps_id": 409, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Champion", "innova_internal_id": 16629, "innova_description": "Champion <PERSON>ana Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON><PERSON>", "original": "Champion <PERSON>ana Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 48, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1443, "mps_id": 494, "candidate_rank": 1, "osl_mold": "<PERSON>winder", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON>winder", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON>winder", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON>winder", "innova_plastic_normalized": "Champion", "innova_internal_id": 16645, "innova_description": "Champion <PERSON><PERSON><PERSON> Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON>winder", "original": "Champion <PERSON><PERSON><PERSON> Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 56, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 7979, "mps_id": 457, "candidate_rank": 1, "osl_mold": "Panther", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Panther", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Panther", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Panther", "innova_plastic_normalized": "Champion", "innova_internal_id": 16447, "innova_description": "Champion Panther Mid-Range", "innova_parsed": {"plastic": "Champion", "mold": "Panther", "original": "Champion Panther Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 62, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 17701, "mps_id": 19378, "candidate_rank": 1, "osl_mold": "Roc", "osl_plastic": "DX Glow", "osl_stamp": "Burst Logo Stock", "osl_weights": "173-175", "osl_color": "Varies", "innova_parsed_mold": "Roc", "innova_parsed_plastic": "DX Classic Glow", "osl_mold_normalized": "Roc", "osl_plastic_normalized": "DX Classic Glow", "innova_mold_normalized": "Roc", "innova_plastic_normalized": "DX Classic Glow", "innova_internal_id": 6758, "innova_description": "DX Classic Glow Roc Mid-Range", "innova_parsed": {"plastic": "DX Classic Glow", "mold": "Roc", "original": "DX Classic Glow Roc Mid-Range"}, "innova_weights": "170-174", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 17702, "mps_id": 19378, "candidate_rank": 1, "osl_mold": "Roc", "osl_plastic": "DX Glow", "osl_stamp": "Burst Logo Stock", "osl_weights": "176-180", "osl_color": "Varies", "innova_parsed_mold": "Roc", "innova_parsed_plastic": "DX Classic Glow", "osl_mold_normalized": "Roc", "osl_plastic_normalized": "DX Classic Glow", "innova_mold_normalized": "Roc", "innova_plastic_normalized": "DX Classic Glow", "innova_internal_id": 6759, "innova_description": "DX Classic Glow Roc Mid-Range", "innova_parsed": {"plastic": "DX Classic Glow", "mold": "Roc", "original": "DX Classic Glow Roc Mid-Range"}, "innova_weights": "175-177", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 8142, "mps_id": 15505, "candidate_rank": 1, "osl_mold": "Corvette", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Corvette", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Corvette", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Corvette", "innova_plastic_normalized": "Champion", "innova_internal_id": 16719, "innova_description": "Champion Corvette Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "Corvette", "original": "Champion Corvette Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 52, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1459, "mps_id": 530, "candidate_rank": 1, "osl_mold": "Tern", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Tern", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Tern", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Tern", "innova_plastic_normalized": "Champion", "innova_internal_id": 16647, "innova_description": "Champion <PERSON>rn Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "Tern", "original": "Champion <PERSON>rn Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 7319, "mps_id": 14996, "candidate_rank": 1, "osl_mold": "TL3", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "TL3", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "TL 3", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "TL 3", "innova_plastic_normalized": "Champion", "innova_internal_id": 16537, "innova_description": "Champion TL3 Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "TL3", "original": "Champion TL3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1342, "mps_id": 316, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "Champion", "innova_internal_id": 16417, "innova_description": "Champion Aviar Putt & Approach", "innova_parsed": {"plastic": "Champion", "mold": "Aviar", "original": "Champion Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 67, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 11930, "mps_id": 16935, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "Champion", "innova_internal_id": 16435, "innova_description": "Champion Invictus Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON><PERSON><PERSON>", "original": "Champion Invictus Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 52, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 10935, "mps_id": 410, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "130-139", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "DX", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "DX", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "DX", "innova_internal_id": 13843, "innova_description": "DX Katana Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "<PERSON><PERSON>", "original": "DX Katana Distance Driver"}, "innova_weights": "100-139 (parsed_less_than)", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 36, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1086, "mps_id": 585, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "DX", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "DX", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "DX", "innova_internal_id": 16579, "innova_description": "DX Wraith Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "<PERSON><PERSON>", "original": "DX Wraith Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 36, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1050, "mps_id": 519, "candidate_rank": 1, "osl_mold": "Teebird", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Teebird", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Teebird", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Teebird", "innova_plastic_normalized": "DX", "innova_internal_id": 16489, "innova_description": "DX Teebird Fairway Driver", "innova_parsed": {"plastic": "DX", "mold": "Teebird", "original": "DX Teebird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 40, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1113, "mps_id": 477, "candidate_rank": 1, "osl_mold": "Roc3", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Roc 3", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Roc 3", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Roc 3", "innova_plastic_normalized": "DX", "innova_internal_id": 16669, "innova_description": "DX Roc 3 Mid-Range", "innova_parsed": {"plastic": "DX", "mold": "Roc 3", "original": "DX Roc 3 Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 39, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1409, "mps_id": 432, "candidate_rank": 1, "osl_mold": "Leopard3", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Leopard3", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Leopard 3", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Leopard 3", "innova_plastic_normalized": "Champion", "innova_internal_id": 16635, "innova_description": "Champion Leopard3 Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "Leopard3", "original": "Champion Leopard3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 53, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2343, "mps_id": 440, "candidate_rank": 1, "osl_mold": "Mako3", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Mako3", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Mako 3", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Mako 3", "innova_plastic_normalized": "Star", "innova_internal_id": 17114, "innova_description": "Star Mako3 Mid-Range", "innova_parsed": {"plastic": "Star", "mold": "Mako3", "original": "Star Mako3 Mid-Range"}, "innova_weights": "151-159", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 50, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2368, "mps_id": 490, "candidate_rank": 1, "osl_mold": "Shark", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Shark", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Shark", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Shark", "innova_plastic_normalized": "Star", "innova_internal_id": 17585, "innova_description": "Star Shark Mid-Range", "innova_parsed": {"plastic": "Star", "mold": "Shark", "original": "Star Shark Mid-Range"}, "innova_weights": "151-159", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 50, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2632, "mps_id": 433, "candidate_rank": 1, "osl_mold": "Leopard3", "osl_plastic": "Gstar", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Leopard3", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "Leopard 3", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "Leopard 3", "innova_plastic_normalized": "G-Star", "innova_internal_id": 16677, "innova_description": "GStar Leopard3 Fairway Driver", "innova_parsed": {"plastic": "GStar", "mold": "Leopard3", "original": "GStar Leopard3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 48, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2785, "mps_id": 329, "candidate_rank": 1, "osl_mold": "Beast", "osl_plastic": "Champion Blizzard", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Beast", "innova_parsed_plastic": "Blizzard Champion", "osl_mold_normalized": "Beast", "osl_plastic_normalized": "Blizzard Champion", "innova_mold_normalized": "Beast", "innova_plastic_normalized": "Blizzard Champion", "innova_internal_id": 16618, "innova_description": "Blizzard Champion Beast Distance Driver", "innova_parsed": {"plastic": "Blizzard Champion", "mold": "Beast", "original": "Blizzard Champion Beast Distance Driver"}, "innova_weights": "151-159", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 59, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 5536, "mps_id": 536, "candidate_rank": 1, "osl_mold": "Thunderbird", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Thunderbird", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Thunderbird", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Thunderbird", "innova_plastic_normalized": "Champion", "innova_internal_id": 16535, "innova_description": "Champion Thunderbird Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "Thunderbird", "original": "Champion Thunderbird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 57, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 15866, "mps_id": 18838, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "Champion", "osl_stamp": "Circle Fade Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "Champion", "innova_internal_id": 16417, "innova_description": "Champion Aviar Putt & Approach", "innova_parsed": {"plastic": "Champion", "mold": "Aviar", "original": "Champion Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 67, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 15860, "mps_id": 18837, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Champion", "osl_stamp": "Circle Fade Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Champion", "innova_internal_id": 16733, "innova_description": "Champion Wraith Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON><PERSON>", "original": "Champion Wraith Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 48, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 15889, "mps_id": 18843, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "Champion", "osl_stamp": "Circle Fade Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "Champion", "innova_internal_id": 16435, "innova_description": "Champion Invictus Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON><PERSON><PERSON>", "original": "Champion Invictus Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 52, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 974, "mps_id": 377, "candidate_rank": 1, "osl_mold": "Destroyer", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Destroyer", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Destroyer", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Destroyer", "innova_plastic_normalized": "DX", "innova_internal_id": 16755, "innova_description": "DX Destroyer Distance Driver", "innova_parsed": {"plastic": "DX", "mold": "Destroyer", "original": "DX Destroyer Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 43, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1004, "mps_id": 426, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "DX", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "DX", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "DX", "innova_internal_id": 16481, "innova_description": "DX Leopard Fairway Driver", "innova_parsed": {"plastic": "DX", "mold": "<PERSON><PERSON>", "original": "DX Leopard Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 40, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1061, "mps_id": 548, "candidate_rank": 1, "osl_mold": "Valkyrie", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Valkyrie", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Valkyrie", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Valkyrie", "innova_plastic_normalized": "DX", "innova_internal_id": 16493, "innova_description": "DX Valkyrie Fairway Driver", "innova_parsed": {"plastic": "DX", "mold": "Valkyrie", "original": "DX Valkyrie Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 42, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1120, "mps_id": 486, "candidate_rank": 1, "osl_mold": "Shark", "osl_plastic": "DX", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Shark", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Shark", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Shark", "innova_plastic_normalized": "DX", "innova_internal_id": 16573, "innova_description": "DX Shark Mid-Range", "innova_parsed": {"plastic": "DX", "mold": "Shark", "original": "DX Shark Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1219, "mps_id": 7692, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "R-Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "R-Pro", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "R-Pro", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "R-Pro", "innova_internal_id": 16805, "innova_description": "R-Pro Aviar Putt & Approach", "innova_parsed": {"plastic": "R-Pro", "mold": "Aviar", "original": "R-Pro Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 63, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1214, "mps_id": 13760, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON> A<PERSON>r", "osl_plastic": "Pro", "osl_stamp": "<PERSON> 5x World Putting Champion", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON> A<PERSON>r", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "<PERSON><PERSON> A<PERSON>r", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "<PERSON><PERSON> A<PERSON>r", "innova_plastic_normalized": "Pro", "innova_internal_id": 16681, "innova_description": "Pro Yeti Aviar Putt & Approach", "innova_parsed": {"plastic": "Pro", "mold": "<PERSON><PERSON> A<PERSON>r", "original": "Pro Yeti Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1240, "mps_id": 458, "candidate_rank": 1, "osl_mold": "Pig", "osl_plastic": "R-Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Pig", "innova_parsed_plastic": "R-Pro", "osl_mold_normalized": "Pig", "osl_plastic_normalized": "R-Pro", "innova_mold_normalized": "Pig", "innova_plastic_normalized": "R-Pro", "innova_internal_id": 16875, "innova_description": "R-Pro Pig Mid-Range", "innova_parsed": {"plastic": "R-Pro", "mold": "Pig", "original": "R-Pro Pig Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1294, "mps_id": 541, "candidate_rank": 1, "osl_mold": "Thunderbird", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Thunderbird", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Thunderbird", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Thunderbird", "innova_plastic_normalized": "Pro", "innova_internal_id": 16913, "innova_description": "Pro Thunderbird Fairway Driver", "innova_parsed": {"plastic": "Pro", "mold": "Thunderbird", "original": "Pro Thunderbird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 50, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1234, "mps_id": 372, "candidate_rank": 1, "osl_mold": "Dart", "osl_plastic": "R-Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Dart", "innova_parsed_plastic": "R-Pro", "osl_mold_normalized": "Dart", "osl_plastic_normalized": "R-Pro", "innova_mold_normalized": "Dart", "innova_plastic_normalized": "R-Pro", "innova_internal_id": 16809, "innova_description": "R-Pro Dart Putt & Approach", "innova_parsed": {"plastic": "R-Pro", "mold": "Dart", "original": "R-Pro Dart Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1274, "mps_id": 430, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Pro", "innova_internal_id": 16865, "innova_description": "Pro Leopard Fairway Driver", "innova_parsed": {"plastic": "Pro", "mold": "<PERSON><PERSON>", "original": "Pro Leopard Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 42, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1259, "mps_id": 16628, "candidate_rank": 1, "osl_mold": "Boss", "osl_plastic": "Pro", "osl_stamp": "1108 Feet World Record Distance Model", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Boss", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Boss", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Boss", "innova_plastic_normalized": "Pro", "innova_internal_id": 16605, "innova_description": "Pro Boss Distance Driver", "innova_parsed": {"plastic": "Pro", "mold": "Boss", "original": "Pro Boss Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 33, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1264, "mps_id": 384, "candidate_rank": 1, "osl_mold": "Destroyer", "osl_plastic": "Pro", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Destroyer", "innova_parsed_plastic": "Pro", "osl_mold_normalized": "Destroyer", "osl_plastic_normalized": "Pro", "innova_mold_normalized": "Destroyer", "innova_plastic_normalized": "Pro", "innova_internal_id": 16609, "innova_description": "Pro Destroyer Distance Driver", "innova_parsed": {"plastic": "Pro", "mold": "Destroyer", "original": "Pro Destroyer Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1355, "mps_id": 15742, "candidate_rank": 1, "osl_mold": "Boss", "osl_plastic": "Champion", "osl_stamp": "1108 Feet World Record Distance Model", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Boss", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Boss", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Boss", "innova_plastic_normalized": "Champion", "innova_internal_id": 16715, "innova_description": "Champion Boss Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "Boss", "original": "Champion Boss Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1414, "mps_id": 441, "candidate_rank": 1, "osl_mold": "Mamba", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Mamba", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Mamba", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Mamba", "innova_plastic_normalized": "Champion", "innova_internal_id": 16637, "innova_description": "Champion Mamba Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "Mamba", "original": "Champion Mamba Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1435, "mps_id": 467, "candidate_rank": 1, "osl_mold": "Roadrunner", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Roadrunner", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Roadrunner", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Roadrunner", "innova_plastic_normalized": "Champion", "innova_internal_id": 16725, "innova_description": "Champion Roadrunner Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "Roadrunner", "original": "Champion Roadrunner Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 56, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1404, "mps_id": 13942, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Champion", "osl_stamp": "<PERSON> 2x World Champion Sig", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Champion", "innova_internal_id": 16633, "innova_description": "Champion <PERSON><PERSON> Fairway Driver", "innova_parsed": {"plastic": "Champion", "mold": "<PERSON><PERSON>", "original": "Champion <PERSON><PERSON> Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 52, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1373, "mps_id": 376, "candidate_rank": 1, "osl_mold": "Destroyer", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Destroyer", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Destroyer", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Destroyer", "innova_plastic_normalized": "Champion", "innova_internal_id": 16525, "innova_description": "Champion Destroyer Distance Driver", "innova_parsed": {"plastic": "Champion", "mold": "Destroyer", "original": "Champion Destroyer Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 53, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 1431, "mps_id": 461, "candidate_rank": 1, "osl_mold": "Rhyno", "osl_plastic": "Champion", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Rhyno", "innova_parsed_plastic": "Champion", "osl_mold_normalized": "Rhyno", "osl_plastic_normalized": "Champion", "innova_mold_normalized": "Rhyno", "innova_plastic_normalized": "Champion", "innova_internal_id": 16641, "innova_description": "Champion Rhyno Putt & Approach", "innova_parsed": {"plastic": "Champion", "mold": "Rhyno", "original": "Champion Rhyno Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2305, "mps_id": 10149, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "DX", "osl_stamp": "Bottom", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "DX", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "DX", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "DX", "innova_internal_id": 16465, "innova_description": "DX Aviar Putt & Approach", "innova_parsed": {"plastic": "DX", "mold": "Aviar", "original": "DX Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 58, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2379, "mps_id": 535, "candidate_rank": 1, "osl_mold": "Tern", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Tern", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Tern", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Tern", "innova_plastic_normalized": "Star", "innova_internal_id": 17029, "innova_description": "Star Tern Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Tern", "original": "Star Tern Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 36, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2334, "mps_id": 481, "candidate_rank": 1, "osl_mold": "Roc3", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Roc3", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Roc 3", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Roc 3", "innova_plastic_normalized": "Star", "innova_internal_id": 17019, "innova_description": "Star Roc3 Mid-Range", "innova_parsed": {"plastic": "Star", "mold": "Roc3", "original": "Star Roc3 Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2332, "mps_id": 542, "candidate_rank": 1, "osl_mold": "Thunderbird", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Thunderbird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Thunderbird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Thunderbird", "innova_plastic_normalized": "Star", "innova_internal_id": 17031, "innova_description": "Star Thunderbird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Thunderbird", "original": "Star Thunderbird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 52, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2335, "mps_id": 348, "candidate_rank": 1, "osl_mold": "Boss", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Boss", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Boss", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Boss", "innova_plastic_normalized": "Star", "innova_internal_id": 16939, "innova_description": "Star Boss Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Boss", "original": "Star Boss Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 97, "string_similarity": 36, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2382, "mps_id": 336, "candidate_rank": 1, "osl_mold": "Beast", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Beast", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Beast", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Beast", "innova_plastic_normalized": "Star", "innova_internal_id": 16937, "innova_description": "Star Beast Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Beast", "original": "Star Beast Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2380, "mps_id": 444, "candidate_rank": 1, "osl_mold": "Mamba", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Mamba", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Mamba", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Mamba", "innova_plastic_normalized": "Star", "innova_internal_id": 16955, "innova_description": "Star Mamba Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "Mamba", "original": "Star Mamba Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 38, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2419, "mps_id": 14144, "candidate_rank": 1, "osl_mold": "Teebird", "osl_plastic": "Star", "osl_stamp": "<PERSON> 2009 PDGA World Champion", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Teebird", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Teebird", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Teebird", "innova_plastic_normalized": "Star", "innova_internal_id": 17025, "innova_description": "Star Teebird Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Teebird", "original": "Star Teebird Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2392, "mps_id": 7686, "candidate_rank": 1, "osl_mold": "Aviar Putter", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Aviar", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Aviar", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Aviar", "innova_plastic_normalized": "Star", "innova_internal_id": 16891, "innova_description": "Star Aviar Putt & Approach", "innova_parsed": {"plastic": "Star", "mold": "Aviar", "original": "Star Aviar Putt & Approach"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 62, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2394, "mps_id": 431, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Star", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Star", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Star", "innova_internal_id": 16949, "innova_description": "Star Leopard Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "<PERSON><PERSON>", "original": "Star Leopard Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 44, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2406, "mps_id": 367, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON><PERSON>", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON><PERSON>", "innova_parsed_plastic": "Star", "osl_mold_normalized": "<PERSON><PERSON><PERSON>", "osl_plastic_normalized": "Star", "innova_mold_normalized": "<PERSON><PERSON><PERSON>", "innova_plastic_normalized": "Star", "innova_internal_id": 16899, "innova_description": "Star Daedalus Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "<PERSON><PERSON><PERSON>", "original": "Star Daedalus Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2426, "mps_id": 13953, "candidate_rank": 1, "osl_mold": "<PERSON><PERSON>", "osl_plastic": "Star", "osl_stamp": "<PERSON> 12 Time World Champion Signature", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "<PERSON><PERSON>", "innova_parsed_plastic": "Star", "osl_mold_normalized": "<PERSON><PERSON>", "osl_plastic_normalized": "Star", "innova_mold_normalized": "<PERSON><PERSON>", "innova_plastic_normalized": "Star", "innova_internal_id": 17041, "innova_description": "Star Wraith Distance Driver", "innova_parsed": {"plastic": "Star", "mold": "<PERSON><PERSON>", "original": "Star Wraith Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 98, "string_similarity": 41, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2634, "mps_id": 439, "candidate_rank": 1, "osl_mold": "Mako3", "osl_plastic": "Gstar", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Mako 3", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "Mako 3", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "Mako 3", "innova_plastic_normalized": "G-Star", "innova_internal_id": 16819, "innova_description": "GStar Mako 3 Mid-Range", "innova_parsed": {"plastic": "GStar", "mold": "Mako 3", "original": "GStar Mako 3 Mid-Range"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 50, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2645, "mps_id": 352, "candidate_rank": 1, "osl_mold": "Coloss<PERSON>", "osl_plastic": "Gstar", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Coloss<PERSON>", "innova_parsed_plastic": "GStar", "osl_mold_normalized": "Coloss<PERSON>", "osl_plastic_normalized": "G-Star", "innova_mold_normalized": "Coloss<PERSON>", "innova_plastic_normalized": "G-Star", "innova_internal_id": 16499, "innova_description": "GStar Colossus Distance Driver", "innova_parsed": {"plastic": "GStar", "mold": "Coloss<PERSON>", "original": "GStar Colossus Distance Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 47, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 2790, "mps_id": 374, "candidate_rank": 1, "osl_mold": "Destroyer", "osl_plastic": "Champion Blizzard", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Destroyer", "innova_parsed_plastic": "Blizzard Champion", "osl_mold_normalized": "Destroyer", "osl_plastic_normalized": "Blizzard Champion", "innova_mold_normalized": "Destroyer", "innova_plastic_normalized": "Blizzard Champion", "innova_internal_id": 16416, "innova_description": "Blizzard Champion Destroyer Distance Driver", "innova_parsed": {"plastic": "Blizzard Champion", "mold": "Destroyer", "original": "Blizzard Champion Destroyer Distance Driver"}, "innova_weights": "151-159", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 63, "status": "🟢 PERFECT_MATCH"}, {"osl_id": 5521, "mps_id": 14070, "candidate_rank": 1, "osl_mold": "Teebird3", "osl_plastic": "Star", "osl_stamp": "Stock", "osl_weights": "151-164", "osl_color": "Varies", "innova_parsed_mold": "Teebird 3", "innova_parsed_plastic": "Star", "osl_mold_normalized": "Teebird 3", "osl_plastic_normalized": "Star", "innova_mold_normalized": "Teebird 3", "innova_plastic_normalized": "Star", "innova_internal_id": 17027, "innova_description": "Star Teebird 3 Fairway Driver", "innova_parsed": {"plastic": "Star", "mold": "Teebird 3", "original": "Star Teebird 3 Fairway Driver"}, "innova_weights": "160-164", "mold_match": true, "plastic_match": true, "weight_match": "OVERLAP", "confidence_score": 99, "string_similarity": 45, "status": "🟢 PERFECT_MATCH"}]